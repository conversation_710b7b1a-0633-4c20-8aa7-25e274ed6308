/**
 * Tests for CourseAssignment component
 * Testing the three main authentication cases:
 * 1. Admin/HR users - Can see all assignments and manage them
 * 2. <PERSON><PERSON> - Can view and manage assignments for their mentees (when menteeId is in URL)
 * 3. Mentees - Can view their own assignments and upload documents
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd components that might cause issues in tests
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock data for different user types
const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockHRUser = {
  _id: 'hr-id',
  name: 'HR User',
  role: ROLES.HR,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockMenteeUser = {
  _id: 'mentee-id',
  name: 'Mentee User',
  role: 'Developer',
  email: '<EMAIL>',
};

// Mock assignment data
const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-id',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/course',
    documents: [
      {
        _id: 'doc-1',
        documentName: 'Certificate.pdf',
        documentLink: 'https://example.com/doc1.pdf',
        approvalStatus: 'pending',
      },
    ],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
  {
    _id: 'assignment-2',
    courseName: 'Node.js Fundamentals',
    duration: '2 Months',
    dueDate: '2024-04-01',
    menteeName: 'Alice Johnson',
    mentorName: 'Bob Wilson',
    employeeId: 'EMP002',
    menteeId: 'mentee-2',
    mentorId: 'mentor-2',
    completionPercentage: 50,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/nodejs-course',
    documents: [],
    menteeComments: '',
    mentorComments: '',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  // Mock getUserData to return the specified user
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock responses
    mockRequest.mockImplementation(url => {
      if (url.includes('/user/details')) {
        return Promise.resolve({ data: mockAdminUser });
      }
      if (url.includes('/tech-roadmap/all-assignments')) {
        return Promise.resolve({ data: mockAssignments });
      }
      return Promise.resolve({ data: [] });
    });
  });

  describe('Basic Component Rendering', () => {
    it('should render the component without crashing', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Wait for component to load - check for the card title
      await waitFor(
        () => {
          expect(
            screen.getByText(/Tech Roadmap & Certifications/),
          ).toBeInTheDocument();
        },
        { timeout: 5000 },
      );
    });

    it('should show search input', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Wait for search input to appear
      await waitFor(() => {
        expect(
          screen.getByPlaceholderText(
            'Search by employee name, course name, or mentor',
          ),
        ).toBeInTheDocument();
      });
    });
  });

  describe('Case 1: Admin/HR User Authentication', () => {
    it('should render admin view with all assignments and management options', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Wait for component to load
      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // Should show "Add a new course" button for admin
      expect(screen.getByText('Assign a new course')).toBeInTheDocument();

      // Should display employee name and ID columns (admin view)
      await waitFor(() => {
        expect(screen.getByText('Employee Name')).toBeInTheDocument();
        expect(screen.getByText('Employee ID')).toBeInTheDocument();
        expect(screen.getByText('Mentors')).toBeInTheDocument();
      });

      // Should show action column with edit/delete options
      await waitFor(() => {
        expect(screen.getByText('Action')).toBeInTheDocument();
      });

      // Should display assignment data
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('EMP001')).toBeInTheDocument();
      });
    });

    it('should allow admin to delete assignments', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/assignment') &&
          options?.method === 'DELETE'
        ) {
          return Promise.resolve({ status: 200 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click delete button
      const deleteButtons = screen.getAllByRole('button');
      const deleteButton = deleteButtons.find(btn =>
        btn.querySelector('[data-icon="delete"]'),
      );

      if (deleteButton) {
        fireEvent.click(deleteButton);

        // Confirm deletion in modal
        await waitFor(() => {
          const confirmButton = screen.getByText('Yes');
          fireEvent.click(confirmButton);
        });
      }
    });

    it('should show view-only comments for admin', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show "View comments" buttons for both mentor and mentee comments
      const viewCommentButtons = screen.getAllByText('View comments');
      expect(viewCommentButtons.length).toBeGreaterThan(0);
    });
  });

  describe('Case 2: HR User Authentication', () => {
    it('should render HR view similar to admin', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockHRUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockHRUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications - Admin'),
        ).toBeInTheDocument();
      });

      // HR should have similar privileges as admin
      expect(screen.getByText('Assign a new course')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Employee Name')).toBeInTheDocument();
        expect(screen.getByText('Employee ID')).toBeInTheDocument();
      });
    });
  });

  describe('Case 3: Mentor User Authentication (with menteeId in URL)', () => {
    it('should render mentor view when menteeId is in URL', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
      });

      // Should show "Assign a new course" button for mentors
      expect(screen.getByText('Assign a new course')).toBeInTheDocument();

      // Should show action column for mentors
      await waitFor(() => {
        expect(screen.getByText('Action')).toBeInTheDocument();
      });
    });

    it('should allow mentor to edit completion percentage', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (
          url.includes('/tech-roadmap/status') &&
          options?.method === 'PATCH'
        ) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find completion percentage input
      const percentageInputs = screen.getAllByDisplayValue('75');
      if (percentageInputs.length > 0) {
        const input = percentageInputs[0];
        fireEvent.change(input, { target: { value: '85' } });
        fireEvent.blur(input);
      }
    });

    it('should allow mentor to add/edit mentor comments', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click mentor comments button
      const editCommentsButtons = screen.getAllByText('Edit comments');
      if (editCommentsButtons.length > 0) {
        fireEvent.click(editCommentsButtons[0]);

        // Should open comments modal
        await waitFor(() => {
          expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
        });
      }
    });

    it('should allow mentor to approve/reject documents', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-id'],
        mockMentorUser,
      );

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/document-approval')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click document icon
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        // Should open document preview modal
        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
          expect(screen.getByText('Reject Document')).toBeInTheDocument();
        });
      }
    });
  });

  describe('Case 4: Mentee User Authentication (own assignments)', () => {
    it('should render mentee view with own assignments only', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          // Return only assignments for this mentee
          const menteeAssignments = mockAssignments.filter(
            assignment => assignment.menteeId === mockMenteeUser._id,
          );
          return Promise.resolve({ data: menteeAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(
          screen.getByText('Tech Roadmap & Certifications'),
        ).toBeInTheDocument();
      });

      // Should NOT show "Assign a new course" button for mentees
      expect(screen.queryByText('Assign a new course')).not.toBeInTheDocument();

      // Should NOT show employee name/ID columns (mentee view)
      expect(screen.queryByText('Employee Name')).not.toBeInTheDocument();
      expect(screen.queryByText('Employee ID')).not.toBeInTheDocument();

      // Should show upload document column
      await waitFor(() => {
        expect(screen.getByText('Upload Document')).toBeInTheDocument();
      });
    });

    it('should allow mentee to upload documents', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      // Mock XMLHttpRequest for file upload
      const mockXHR = {
        open: jest.fn(),
        send: jest.fn(),
        setRequestHeader: jest.fn(),
        onload: null,
        onerror: null,
        status: 200,
        responseText: JSON.stringify({ status: 200 }),
      };

      global.XMLHttpRequest = jest.fn(() => mockXHR);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          const menteeAssignments = mockAssignments.filter(
            assignment => assignment.menteeId === mockMenteeUser._id,
          );
          return Promise.resolve({ data: menteeAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find upload button
      const uploadButtons = screen.getAllByText('Upload');
      if (uploadButtons.length > 0) {
        fireEvent.click(uploadButtons[0]);

        // Simulate file selection
        const file = new File(['test content'], 'test.pdf', {
          type: 'application/pdf',
        });

        // Mock file input creation and trigger
        const createElementSpy = jest.spyOn(document, 'createElement');
        const mockFileInput = {
          type: 'file',
          accept: '.pdf,.jpg,.jpeg,.png',
          style: { display: 'none' },
          onchange: null,
          click: jest.fn(),
          files: [file],
        };

        createElementSpy.mockReturnValue(mockFileInput);

        // Trigger file selection
        if (mockFileInput.onchange) {
          mockFileInput.onchange({ target: { files: [file] } });
        }

        // Simulate successful upload
        setTimeout(() => {
          if (mockXHR.onload) {
            mockXHR.onload();
          }
        }, 100);
      }
    });

    it('should allow mentee to add/edit mentee comments', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMenteeUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          const menteeAssignments = mockAssignments.filter(
            assignment => assignment.menteeId === mockMenteeUser._id,
          );
          return Promise.resolve({ data: menteeAssignments });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click mentee comments button
      const editCommentsButtons = screen.getAllByText('Edit comments');
      if (editCommentsButtons.length > 0) {
        fireEvent.click(editCommentsButtons[0]);

        // Should open comments modal
        await waitFor(() => {
          expect(screen.getByText('Mentee Comments:')).toBeInTheDocument();
        });
      }
    });

    it('should show read-only mentor comments for mentee', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMenteeUser);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Should show "View comments" for mentor comments (read-only)
      const viewCommentButtons = screen.getAllByText('View comments');
      expect(viewCommentButtons.length).toBeGreaterThan(0);
    });
  });
});
