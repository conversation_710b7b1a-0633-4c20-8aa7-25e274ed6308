/**
 * Search and Filter Tests for CourseAssignment component
 * Testing search functionality and data filtering
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

// Extended mock assignment data for search testing
const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-1',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/react-course',
    documents: [],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
  {
    _id: 'assignment-2',
    courseName: 'Node.js Fundamentals',
    duration: '2 Months',
    dueDate: '2024-04-01',
    menteeName: 'Alice Johnson',
    mentorName: 'Bob Wilson',
    employeeId: 'EMP002',
    menteeId: 'mentee-2',
    mentorId: 'mentor-2',
    completionPercentage: 50,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/nodejs-course',
    documents: [],
    menteeComments: '',
    mentorComments: '',
  },
  {
    _id: 'assignment-3',
    courseName: 'Python for Data Science',
    duration: '4 Months',
    dueDate: '2024-05-01',
    menteeName: 'Charlie Brown',
    mentorName: 'Diana Prince',
    employeeId: 'EMP003',
    menteeId: 'mentee-3',
    mentorId: 'mentor-3',
    completionPercentage: 25,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/python-course',
    documents: [],
    menteeComments: 'Just started',
    mentorComments: 'Good beginning',
  },
  {
    _id: 'assignment-4',
    courseName: 'JavaScript ES6+',
    duration: '1 Month',
    dueDate: '2024-02-01',
    menteeName: 'David Lee',
    mentorName: 'Jane Smith',
    employeeId: 'EMP004',
    menteeId: 'mentee-4',
    mentorId: 'mentor-id',
    completionPercentage: 100,
    completionStatus: 'Completed',
    learningMedium: 'https://example.com/js-course',
    documents: [],
    menteeComments: 'Completed successfully',
    mentorComments: 'Excellent work',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Search and Filter', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest.mockImplementation(url => {
      if (url.includes('/user/details')) {
        return Promise.resolve({ data: mockAdminUser });
      }
      if (url.includes('/tech-roadmap/all-assignments')) {
        return Promise.resolve({ data: mockAssignments });
      }
      return Promise.resolve({ data: [] });
    });
  });

  describe('Search Functionality', () => {
    it('should filter assignments by employee name', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
      });

      // Search for "John"
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'John' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
      });
    });

    it('should filter assignments by course name', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Python for Data Science')).toBeInTheDocument();
      });

      // Search for "Python"
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'Python' } });

      await waitFor(() => {
        expect(screen.getByText('Python for Data Science')).toBeInTheDocument();
        expect(screen.queryByText('React Advanced')).not.toBeInTheDocument();
      });
    });

    it('should filter assignments by mentor name', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('JavaScript ES6+')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
      });

      // Search for "Jane Smith" (mentor for 2 assignments)
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'Jane Smith' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('JavaScript ES6+')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
      });
    });

    it('should handle case-insensitive search', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Search with different case
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'REACT' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
      });
    });

    it('should show no results for non-matching search', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Search for non-existing term
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'NonExistentCourse' } });

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should clear search and show all results', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
      });

      // Search for specific term
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'React' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
      });

      // Clear search
      fireEvent.change(searchInput, { target: { value: '' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
      });
    });
  });

  describe('Role-based Filtering', () => {
    it('should filter assignments for mentor when menteeId is in URL', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-1'],
        mockMentorUser,
      );

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should only show assignments for the specific mentee
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
      });
    });

    it('should show assignments for mentor based on mentorId', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockMentorUser);

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should show assignments where this user is the mentor
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('JavaScript ES6+')).toBeInTheDocument();
        expect(
          screen.queryByText('Node.js Fundamentals'),
        ).not.toBeInTheDocument();
      });
    });

    it('should show all assignments for admin/HR users', async () => {
      const Wrapper = createWrapper(['/course-assignment'], mockAdminUser);

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      // Should show all assignments
      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
        expect(screen.getByText('Node.js Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Python for Data Science')).toBeInTheDocument();
        expect(screen.getByText('JavaScript ES6+')).toBeInTheDocument();
      });
    });
  });

  describe('Combined Search and Filter', () => {
    it('should apply search on top of role-based filtering', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-1'],
        mockMentorUser,
      );

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Search within the filtered results
      const searchInput = screen.getByPlaceholderText(
        'Search by employee name, course name, or mentor',
      );
      fireEvent.change(searchInput, { target: { value: 'React' } });

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Search for something not in the filtered results
      fireEvent.change(searchInput, { target: { value: 'Node.js' } });

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });
  });
});
