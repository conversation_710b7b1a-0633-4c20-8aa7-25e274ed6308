/**
 * Modal Interactions Tests for CourseAssignment component
 * Testing modal functionality, document preview, and comments
 */

/* eslint-disable react/prop-types */
import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

// Mock TechRoadmap component
jest.mock(
  '../../TechRoadmap',
  () =>
    function MockTechRoadmap({ visible, onCancel, onSuccess }) {
      if (!visible) return null;
      return (
        <div data-testid="tech-roadmap-modal">
          <h2>Add a new course</h2>
          <button onClick={onCancel}>Cancel</button>
          <button onClick={onSuccess}>Submit</button>
        </div>
      );
    },
);

// Mock MenteeRoadmapForm component
jest.mock(
  '../../MenteeTechRoadmap/MenteeRoadmapForm',
  () =>
    function MockMenteeRoadmapForm({ onSuccess, onCancel }) {
      return (
        <div data-testid="mentee-roadmap-form">
          <h2>Assign a New Course</h2>
          <button onClick={onCancel}>Cancel</button>
          <button onClick={onSuccess}>Submit</button>
        </div>
      );
    },
);

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const mockMentorUser = {
  _id: 'mentor-id',
  name: 'Mentor User',
  role: 'Mentor',
  email: '<EMAIL>',
};

const mockAssignments = [
  {
    _id: 'assignment-1',
    courseName: 'React Advanced',
    duration: '3 Months',
    dueDate: '2024-03-01',
    menteeName: 'John Doe',
    mentorName: 'Jane Smith',
    employeeId: 'EMP001',
    menteeId: 'mentee-1',
    mentorId: 'mentor-id',
    completionPercentage: 75,
    completionStatus: 'Assigned',
    learningMedium: 'https://example.com/react-course',
    documents: [
      {
        _id: 'doc-1',
        documentName: 'Certificate.pdf',
        documentLink: 'https://example.com/doc1.pdf',
        approvalStatus: 'pending',
      },
    ],
    menteeComments: 'Making good progress',
    mentorComments: 'Keep up the good work',
  },
];

const createWrapper = (
  initialEntries = ['/course-assignment'],
  user = mockAdminUser,
) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);

  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>{children}</ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Modal Interactions', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest.mockImplementation(url => {
      if (url.includes('/user/details')) {
        return Promise.resolve({ data: mockAdminUser });
      }
      if (url.includes('/tech-roadmap/all-assignments')) {
        return Promise.resolve({ data: mockAssignments });
      }
      return Promise.resolve({ data: [] });
    });
  });

  describe('TechRoadmap Modal (Add Course)', () => {
    it('should open and close TechRoadmap modal for super admin', async () => {
      // Mock isSuperAdmin to return true
      jest.doMock('../../components/SideBar', () => ({
        isSuperAdmin: jest.fn(() => true),
      }));

      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('Add a new course')).toBeInTheDocument();
      });

      // Click "Add a new course" button
      fireEvent.click(screen.getByText('Add a new course'));

      // Modal should open
      await waitFor(() => {
        expect(screen.getByTestId('tech-roadmap-modal')).toBeInTheDocument();
      });

      // Click cancel to close modal
      fireEvent.click(screen.getByText('Cancel'));

      // Modal should close
      await waitFor(() => {
        expect(
          screen.queryByTestId('tech-roadmap-modal'),
        ).not.toBeInTheDocument();
      });
    });

    it('should handle successful course creation', async () => {
      jest.doMock('../../components/SideBar', () => ({
        isSuperAdmin: jest.fn(() => true),
      }));

      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('Add a new course')).toBeInTheDocument();
      });

      // Open modal
      fireEvent.click(screen.getByText('Add a new course'));

      await waitFor(() => {
        expect(screen.getByTestId('tech-roadmap-modal')).toBeInTheDocument();
      });

      // Submit form
      fireEvent.click(screen.getByText('Submit'));

      // Modal should close and assignments should refresh
      await waitFor(() => {
        expect(
          screen.queryByTestId('tech-roadmap-modal'),
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('MenteeRoadmapForm Modal (Assign Course)', () => {
    it('should open and close MenteeRoadmapForm modal for admin', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('Assign a new course')).toBeInTheDocument();
      });

      // Click "Assign a new course" button
      fireEvent.click(screen.getByText('Assign a new course'));

      // Modal should open
      await waitFor(() => {
        expect(screen.getByTestId('mentee-roadmap-form')).toBeInTheDocument();
      });

      // Click cancel to close modal
      fireEvent.click(screen.getByText('Cancel'));

      // Modal should close
      await waitFor(() => {
        expect(
          screen.queryByTestId('mentee-roadmap-form'),
        ).not.toBeInTheDocument();
      });
    });

    it('should handle successful course assignment', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('Assign a new course')).toBeInTheDocument();
      });

      // Open modal
      fireEvent.click(screen.getByText('Assign a new course'));

      await waitFor(() => {
        expect(screen.getByTestId('mentee-roadmap-form')).toBeInTheDocument();
      });

      // Submit form
      fireEvent.click(screen.getByText('Submit'));

      // Modal should close and assignments should refresh
      await waitFor(() => {
        expect(
          screen.queryByTestId('mentee-roadmap-form'),
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('Document Preview Modal', () => {
    it('should open document preview modal when clicking document icon', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click document button
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        // Document preview modal should open
        await waitFor(() => {
          expect(screen.getByText(/Preview/)).toBeInTheDocument();
        });
      }
    });

    it('should close document preview modal', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open document preview
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        await waitFor(() => {
          expect(screen.getByText(/Preview/)).toBeInTheDocument();
        });

        // Close modal
        fireEvent.click(screen.getByText('Close'));

        await waitFor(() => {
          expect(screen.queryByText(/Preview/)).not.toBeInTheDocument();
        });
      }
    });

    it('should show approval buttons for mentors', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-1'],
        mockMentorUser,
      );

      mockRequest.mockImplementation(url => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open document preview
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
          expect(screen.getByText('Reject Document')).toBeInTheDocument();
        });
      }
    });

    it('should handle document approval', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-1'],
        mockMentorUser,
      );

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/document-approval')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open document preview and approve
      const documentButtons = screen.getAllByRole('button');
      const documentButton = documentButtons.find(btn =>
        btn.querySelector('[data-icon="file"]'),
      );

      if (documentButton) {
        fireEvent.click(documentButton);

        await waitFor(() => {
          expect(screen.getByText('Approve Document')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Approve Document'));

        // Should show success message
        await waitFor(() => {
          expect(require('antd').message.success).toHaveBeenCalledWith(
            'Document approved successfully',
          );
        });
      }
    });
  });

  describe('Comments Modal', () => {
    it('should open comments modal when clicking view/edit comments', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click view comments button
      const viewCommentsButtons = screen.getAllByText('View comments');
      if (viewCommentsButtons.length > 0) {
        fireEvent.click(viewCommentsButtons[0]);

        // Comments modal should open
        await waitFor(() => {
          expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
          expect(screen.getByText('Mentee Comments:')).toBeInTheDocument();
        });
      }
    });

    it('should close comments modal', async () => {
      const Wrapper = createWrapper();

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Open comments modal
      const viewCommentsButtons = screen.getAllByText('View comments');
      if (viewCommentsButtons.length > 0) {
        fireEvent.click(viewCommentsButtons[0]);

        await waitFor(() => {
          expect(screen.getByText('Mentor Comments:')).toBeInTheDocument();
        });

        // Close modal
        fireEvent.click(screen.getByText('Close'));

        await waitFor(() => {
          expect(
            screen.queryByText('Mentor Comments:'),
          ).not.toBeInTheDocument();
        });
      }
    });

    it('should allow editing comments for authorized users', async () => {
      const Wrapper = createWrapper(
        ['/course-assignment?menteeId=mentee-1'],
        mockMentorUser,
      );

      mockRequest.mockImplementation((url, options) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockMentorUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: mockAssignments });
        }
        if (url.includes('/tech-roadmap/update-comments')) {
          return Promise.resolve({ status: 1 });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>,
      );

      await waitFor(() => {
        expect(screen.getByText('React Advanced')).toBeInTheDocument();
      });

      // Find and click edit comments button
      const editCommentsButtons = screen.getAllByText('Edit comments');
      if (editCommentsButtons.length > 0) {
        fireEvent.click(editCommentsButtons[0]);

        await waitFor(() => {
          expect(screen.getByText('Save Comments')).toBeInTheDocument();
        });

        // Edit comment and save
        const textareas = screen.getAllByRole('textbox');
        if (textareas.length > 0) {
          fireEvent.change(textareas[0], {
            target: { value: 'Updated comment' },
          });
          fireEvent.click(screen.getByText('Save Comments'));

          await waitFor(() => {
            expect(require('antd').message.success).toHaveBeenCalledWith(
              'Mentor comments saved successfully',
            );
          });
        }
      }
    });
  });
});
