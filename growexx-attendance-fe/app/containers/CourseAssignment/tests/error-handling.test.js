/**
 * Error Handling Tests for CourseAssignment component
 * Testing various error scenarios and edge cases
 */

import React from 'react';
import { render, waitFor, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import { createMemoryHistory } from 'history';
import configureStore from '../../../configureStore';
import CourseAssignment from '../index';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import { message } from 'antd';

// Mock the request utility
jest.mock('utils/request');
const mockRequest = request as jest.MockedFunction<typeof request>;

// Mock the getUserData utility
jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn(),
}));

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    open: jest.fn(),
  },
}));

const mockAdminUser = {
  _id: 'admin-id',
  name: 'Admin User',
  role: ROLES.ADMIN,
  email: '<EMAIL>',
};

const createWrapper = (initialEntries = ['/course-assignment'], user = mockAdminUser) => {
  const history = createMemoryHistory({ initialEntries });
  const store = configureStore({}, history);
  
  const { getUserData } = require('../../../utils/Helper');
  getUserData.mockReturnValue(user);

  return ({ children }) => (
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          {children}
        </ConnectedRouter>
      </IntlProvider>
    </Provider>
  );
};

describe('CourseAssignment Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Error Scenarios', () => {
    it('should handle user details fetch failure', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.reject(new Error('Failed to fetch user details'));
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('Failed to fetch user details');
      });
    });

    it('should handle assignments fetch failure', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.reject(new Error('Failed to fetch assignments'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('Failed to fetch assignments');
      });
    });

    it('should handle empty assignments response', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('No courses assigned yet')).toBeInTheDocument();
      });
    });

    it('should handle malformed assignments response', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: null });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('Failed to fetch assignments');
      });
    });

    it('should handle network timeout errors', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: mockAdminUser });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.reject(new Error('Network timeout'));
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('Failed to fetch assignments');
      });
    });
  });

  describe('File Upload Error Scenarios', () => {
    it('should handle invalid file type upload', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: { ...mockAdminUser, role: 'Developer' } });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ 
            data: [{
              _id: 'assignment-1',
              courseName: 'Test Course',
              menteeId: 'admin-id',
            }] 
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Course')).toBeInTheDocument();
      });

      // Simulate invalid file type
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      
      const createElementSpy = jest.spyOn(document, 'createElement');
      const mockFileInput = {
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        style: { display: 'none' },
        onchange: null,
        click: jest.fn(),
        files: [file],
      };
      
      createElementSpy.mockReturnValue(mockFileInput);
      
      const uploadButtons = screen.getAllByText('Upload');
      if (uploadButtons.length > 0) {
        uploadButtons[0].click();
        
        if (mockFileInput.onchange) {
          mockFileInput.onchange({ target: { files: [file] } });
        }
        
        await waitFor(() => {
          expect(message.error).toHaveBeenCalledWith(
            'Invalid file type. Only PDF, JPEG, JPG, and PNG files are allowed'
          );
        });
      }
    });

    it('should handle file size limit exceeded', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: { ...mockAdminUser, role: 'Developer' } });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ 
            data: [{
              _id: 'assignment-1',
              courseName: 'Test Course',
              menteeId: 'admin-id',
            }] 
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Course')).toBeInTheDocument();
      });

      // Create a large file (over 5MB)
      const largeContent = 'x'.repeat(6 * 1024 * 1024); // 6MB
      const file = new File([largeContent], 'large.pdf', { type: 'application/pdf' });
      
      const createElementSpy = jest.spyOn(document, 'createElement');
      const mockFileInput = {
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        style: { display: 'none' },
        onchange: null,
        click: jest.fn(),
        files: [file],
      };
      
      createElementSpy.mockReturnValue(mockFileInput);
      
      const uploadButtons = screen.getAllByText('Upload');
      if (uploadButtons.length > 0) {
        uploadButtons[0].click();
        
        if (mockFileInput.onchange) {
          mockFileInput.onchange({ target: { files: [file] } });
        }
        
        await waitFor(() => {
          expect(message.error).toHaveBeenCalledWith('File size exceeds 5MB limit');
        });
      }
    });

    it('should handle upload network failure', async () => {
      const Wrapper = createWrapper();
      
      // Mock XMLHttpRequest for failed upload
      const mockXHR = {
        open: jest.fn(),
        send: jest.fn(),
        setRequestHeader: jest.fn(),
        onload: null,
        onerror: null,
        status: 500,
        responseText: JSON.stringify({ message: 'Server error' }),
      };
      
      global.XMLHttpRequest = jest.fn(() => mockXHR);

      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: { ...mockAdminUser, role: 'Developer' } });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ 
            data: [{
              _id: 'assignment-1',
              courseName: 'Test Course',
              menteeId: 'admin-id',
            }] 
          });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Course')).toBeInTheDocument();
      });

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      
      const createElementSpy = jest.spyOn(document, 'createElement');
      const mockFileInput = {
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        style: { display: 'none' },
        onchange: null,
        click: jest.fn(),
        files: [file],
      };
      
      createElementSpy.mockReturnValue(mockFileInput);
      
      const uploadButtons = screen.getAllByText('Upload');
      if (uploadButtons.length > 0) {
        uploadButtons[0].click();
        
        if (mockFileInput.onchange) {
          mockFileInput.onchange({ target: { files: [file] } });
        }
        
        // Simulate upload failure
        setTimeout(() => {
          if (mockXHR.onerror) {
            mockXHR.onerror();
          }
        }, 100);
      }
    });
  });

  describe('Permission and Authorization Errors', () => {
    it('should handle unauthorized access gracefully', async () => {
      const Wrapper = createWrapper();
      
      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.reject({ status: 401, message: 'Unauthorized' });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('Failed to fetch user details');
      });
    });

    it('should handle missing user data', async () => {
      const Wrapper = createWrapper();
      
      const { getUserData } = require('../../../utils/Helper');
      getUserData.mockReturnValue(null);

      mockRequest.mockImplementation((url) => {
        if (url.includes('/user/details')) {
          return Promise.resolve({ data: null });
        }
        if (url.includes('/tech-roadmap/all-assignments')) {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      render(
        <Wrapper>
          <CourseAssignment />
        </Wrapper>
      );

      // Component should still render but with limited functionality
      await waitFor(() => {
        expect(screen.getByText('Tech Roadmap & Certifications')).toBeInTheDocument();
      });
    });
  });
});
